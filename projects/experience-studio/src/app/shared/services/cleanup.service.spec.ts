import { TestBed } from '@angular/core/testing';
import { CleanupService } from './cleanup.service';

describe('CleanupService', () => {
  let service: CleanupService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [CleanupService]
    });
    service = TestBed.inject(CleanupService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should track and cleanup timers', () => {
    const timerId = setTimeout(() => {}, 1000);
    service.addTimer(timerId);
    
    const stats = service.getCleanupStats();
    expect(stats.timers).toBe(1);
    
    service.ngOnDestroy();
    
    // Timer should be cleared
    const statsAfterDestroy = service.getCleanupStats();
    expect(statsAfterDestroy.timers).toBe(0);
  });

  it('should track and cleanup intervals', () => {
    const intervalId = setInterval(() => {}, 1000);
    service.addInterval(intervalId);
    
    const stats = service.getCleanupStats();
    expect(stats.intervals).toBe(1);
    
    service.ngOnDestroy();
    
    // Interval should be cleared
    const statsAfterDestroy = service.getCleanupStats();
    expect(statsAfterDestroy.intervals).toBe(0);
  });

  it('should track and cleanup observers', () => {
    if (typeof ResizeObserver !== 'undefined') {
      const observer = new ResizeObserver(() => {});
      service.addObserver(observer);
      
      const stats = service.getCleanupStats();
      expect(stats.observers).toBe(1);
      
      service.ngOnDestroy();
      
      // Observer should be disconnected
      const statsAfterDestroy = service.getCleanupStats();
      expect(statsAfterDestroy.observers).toBe(0);
    }
  });

  it('should prevent adding resources after destruction', () => {
    service.ngOnDestroy();
    
    const timerId = setTimeout(() => {}, 1000);
    service.addTimer(timerId);
    
    // Timer should not be tracked since service is destroyed
    const stats = service.getCleanupStats();
    expect(stats.timers).toBe(0);
    
    // Clean up the timer manually since service won't
    clearTimeout(timerId);
  });

  it('should handle manual timer removal', () => {
    const timerId = setTimeout(() => {}, 1000);
    service.addTimer(timerId);
    
    let stats = service.getCleanupStats();
    expect(stats.timers).toBe(1);
    
    service.removeTimer(timerId);
    
    stats = service.getCleanupStats();
    expect(stats.timers).toBe(0);
  });

  it('should handle manual interval removal', () => {
    const intervalId = setInterval(() => {}, 1000);
    service.addInterval(intervalId);
    
    let stats = service.getCleanupStats();
    expect(stats.intervals).toBe(1);
    
    service.removeInterval(intervalId);
    
    stats = service.getCleanupStats();
    expect(stats.intervals).toBe(0);
  });
});
