import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { createLogger } from '../utils/logger';

/**
 * Service for managing component lifecycle cleanup to prevent memory leaks.
 * Handles subscriptions, timers, intervals, observers, and animation frames.
 *
 * Usage:
 * 1. Inject CleanupService in component providers
 * 2. Register resources using add* methods
 * 3. Service automatically cleans up on component destroy
 */
@Injectable()
export class CleanupService implements OnDestroy {
  private readonly logger = createLogger('CleanupService');

  private readonly subscriptions = new Subscription();
  private readonly timers = new Set<NodeJS.Timeout | number>();
  private readonly intervals = new Set<NodeJS.Timeout | number>();
  private readonly observers = new Set<ResizeObserver | IntersectionObserver | MutationObserver>();
  private readonly animationFrames = new Set<number>();
  private readonly eventListeners = new Map<EventTarget, Map<string, EventListener>>();

  private isDestroyed = false;

  /**
   * Add a subscription to be automatically unsubscribed on destroy
   */
  addSubscription(subscription: Subscription): void {
    if (this.isDestroyed) {
      this.logger.warn('Attempting to add subscription after cleanup service destroyed');
      subscription.unsubscribe();
      return;
    }

    this.subscriptions.add(subscription);
  }

  /**
   * Add a timeout to be automatically cleared on destroy
   */
  addTimer(timerId: NodeJS.Timeout | number): void {
    if (this.isDestroyed) {
      this.logger.warn('Attempting to add timer after cleanup service destroyed');
      clearTimeout(timerId);
      return;
    }

    this.timers.add(timerId);
  }

  /**
   * Add an interval to be automatically cleared on destroy
   */
  addInterval(intervalId: NodeJS.Timeout | number): void {
    if (this.isDestroyed) {
      this.logger.warn('Attempting to add interval after cleanup service destroyed');
      clearInterval(intervalId);
      return;
    }

    this.intervals.add(intervalId);
  }

  /**
   * Add an observer to be automatically disconnected on destroy
   */
  addObserver(observer: ResizeObserver | IntersectionObserver | MutationObserver): void {
    if (this.isDestroyed) {
      this.logger.warn('Attempting to add observer after cleanup service destroyed');
      observer.disconnect();
      return;
    }

    this.observers.add(observer);
  }

  /**
   * Add an animation frame to be automatically cancelled on destroy
   */
  addAnimationFrame(frameId: number): void {
    if (this.isDestroyed) {
      this.logger.warn('Attempting to add animation frame after cleanup service destroyed');
      cancelAnimationFrame(frameId);
      return;
    }

    this.animationFrames.add(frameId);
  }

  /**
   * Add an event listener to be automatically removed on destroy
   */
  addEventListener(
    target: EventTarget,
    type: string,
    listener: EventListener,
    options?: boolean | AddEventListenerOptions
  ): void {
    if (this.isDestroyed) {
      this.logger.warn('Attempting to add event listener after cleanup service destroyed');
      return;
    }

    // Add the event listener
    target.addEventListener(type, listener, options);

    // Track for cleanup
    if (!this.eventListeners.has(target)) {
      this.eventListeners.set(target, new Map());
    }
    this.eventListeners.get(target)!.set(type, listener);
  }

  /**
   * Manually remove a timer (useful for conditional cleanup)
   */
  removeTimer(timerId: NodeJS.Timeout | number): void {
    if (this.timers.has(timerId)) {
      clearTimeout(timerId);
      this.timers.delete(timerId);
    }
  }

  /**
   * Manually remove an interval (useful for conditional cleanup)
   */
  removeInterval(intervalId: NodeJS.Timeout | number): void {
    if (this.intervals.has(intervalId)) {
      clearInterval(intervalId);
      this.intervals.delete(intervalId);
    }
  }

  /**
   * Get cleanup statistics for debugging
   */
  getCleanupStats(): {
    subscriptions: number;
    timers: number;
    intervals: number;
    observers: number;
    animationFrames: number;
    eventListeners: number;
  } {
    return {
      subscriptions: this.subscriptions.closed ? 0 : 1, // Subscription doesn't expose count
      timers: this.timers.size,
      intervals: this.intervals.size,
      observers: this.observers.size,
      animationFrames: this.animationFrames.size,
      eventListeners: Array.from(this.eventListeners.values())
        .reduce((total, map) => total + map.size, 0)
    };
  }

  ngOnDestroy(): void {
    if (this.isDestroyed) {
      this.logger.warn('CleanupService.ngOnDestroy called multiple times');
      return;
    }

    const stats = this.getCleanupStats();
    this.logger.info('Cleaning up resources:', stats);

    // Mark as destroyed first to prevent new registrations
    this.isDestroyed = true;

    // Clear all subscriptions
    if (!this.subscriptions.closed) {
      this.subscriptions.unsubscribe();
    }

    // Clear all timers
    this.timers.forEach(id => {
      try {
        clearTimeout(id);
      } catch (error) {
        this.logger.warn('Error clearing timer:', error);
      }
    });
    this.timers.clear();

    // Clear all intervals
    this.intervals.forEach(id => {
      try {
        clearInterval(id);
      } catch (error) {
        this.logger.warn('Error clearing interval:', error);
      }
    });
    this.intervals.clear();

    // Disconnect all observers
    this.observers.forEach(observer => {
      try {
        observer.disconnect();
      } catch (error) {
        this.logger.warn('Error disconnecting observer:', error);
      }
    });
    this.observers.clear();

    // Cancel all animation frames
    this.animationFrames.forEach(id => {
      try {
        cancelAnimationFrame(id);
      } catch (error) {
        this.logger.warn('Error cancelling animation frame:', error);
      }
    });
    this.animationFrames.clear();

    // Remove all event listeners
    this.eventListeners.forEach((listeners, target) => {
      listeners.forEach((listener, type) => {
        try {
          target.removeEventListener(type, listener);
        } catch (error) {
          this.logger.warn('Error removing event listener:', error);
        }
      });
    });
    this.eventListeners.clear();

    this.logger.info('Cleanup completed successfully');
  }
}
