import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetector<PERSON>ef, <PERSON><PERSON>one } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BehaviorSubject } from 'rxjs';
import { createLogger } from '../../utils';
import { Nl2brPipe } from '../../pipes/nl2br.pipe';

@Component({
  selector: 'app-test-split-screen',
  standalone: true,
  imports: [CommonModule, Nl2brPipe],
  templateUrl: './test-split-screen.component.html',
  styleUrls: ['./test-split-screen.component.scss']
})
export class TestSplitScreenComponent implements OnInit, OnDestroy {
  private readonly logger = createLogger('TestSplitScreenComponent');

  // Resizer state
  isResizing$ = new BehaviorSubject<boolean>(false);

  // Panel widths
  defaultLeftPanelWidth = '40%';
  defaultRightPanelWidth = '60%';

  // Iframe URL
  iframeUrl = 'https://users-01df7a8f-8af7-477a-9e69-7d3a236fa774-96e39bd9-6-c70f3b.azurewebsites.net/';

  // Dummy content for left panel
  dummyContent = [
    {
      title: 'Welcome to Split Screen Test',
      content: 'This is a test component to verify resizer functionality with iframe content.'
    },
    {
      title: 'Resizer Features',
      content: 'The resizer should work smoothly even when dragging over the iframe on the right side.'
    },
    {
      title: 'Test Instructions',
      content: '1. Try dragging the resizer handle\n2. Drag over the iframe area\n3. Verify smooth resizing behavior\n4. Check that iframe remains interactive after resize'
    },
    {
      title: 'Expected Behavior',
      content: 'The resizer should:\n• Work consistently across all areas\n• Not interfere with iframe interaction\n• Provide smooth visual feedback\n• Maintain proper cursor states'
    }
  ];

  constructor(
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone
  ) {}

  ngOnInit(): void {
    this.logger.info('TestSplitScreenComponent initialized');
  }

  ngOnDestroy(): void {
    // Clean up any active resize operation
    if (this.isResizing$.value) {
      const leftPanel = document.querySelector('.test-left-panel') as HTMLElement;
      const rightPanel = document.querySelector('.test-right-panel') as HTMLElement;
      const resizer = document.querySelector('.test-resizer') as HTMLElement;
      const iframe = document.querySelector('.test-iframe') as HTMLIFrameElement;
      const overlay = document.querySelector('.test-resize-overlay');

      if (leftPanel) leftPanel.classList.remove('dragging');
      if (rightPanel) rightPanel.classList.remove('dragging');
      if (resizer) resizer.classList.remove('active');

      // Re-enable iframe pointer events
      if (iframe) {
        iframe.style.pointerEvents = 'auto';
      }

      // Remove resize overlay
      if (overlay) {
        overlay.remove();
      }

      this.isResizing$.next(false);
    }

    this.logger.info('TestSplitScreenComponent destroyed');
  }

  /**
   * Starts the resize operation for the split screen
   */
  startResize(event: MouseEvent): void {
    event.preventDefault();

    this.logger.info('Starting resize operation');
    this.isResizing$.next(true);

    // Get DOM elements
    const leftPanel = document.querySelector('.test-left-panel') as HTMLElement;
    const rightPanel = document.querySelector('.test-right-panel') as HTMLElement;
    const resizer = document.querySelector('.test-resizer') as HTMLElement;
    const container = document.querySelector('.test-split-container') as HTMLElement;
    const iframe = document.querySelector('.test-iframe') as HTMLIFrameElement;

    if (!leftPanel || !rightPanel || !container || !resizer) {
      this.logger.error('Required DOM elements not found for resize operation');
      return;
    }

    // Add visual feedback
    resizer.classList.add('active');
    leftPanel.classList.add('dragging');
    rightPanel.classList.add('dragging');
    document.body.classList.add('user-select-none');

    // CRITICAL FIX: Disable iframe pointer events during resize
    if (iframe) {
      iframe.style.pointerEvents = 'none';
      this.logger.debug('Disabled iframe pointer events for resize operation');
    }

    // Create overlay to capture mouse events over iframe during resize
    const resizeOverlay = document.createElement('div');
    resizeOverlay.className = 'test-resize-overlay';
    resizeOverlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 9999;
      cursor: col-resize;
      background: transparent;
      user-select: none;
      pointer-events: all;
    `;
    document.body.appendChild(resizeOverlay);

    // Store initial positions
    const initialX = event.clientX;
    const containerWidth = container.offsetWidth;
    const initialLeftWidth = leftPanel.offsetWidth;
    const minWidth = 200;
    const maxLeftWidth = containerWidth - minWidth;
    const minLeftWidth = minWidth;

    // Track animation frame for smoother performance
    let animationFrameId: number | null = null;

    // Create mousemove handler
    const handleMouseMove = (e: MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId);
      }

      animationFrameId = requestAnimationFrame(() => {
        const dx = e.clientX - initialX;
        let newLeftWidth = initialLeftWidth + dx;

        // Apply constraints
        newLeftWidth = Math.max(minLeftWidth, Math.min(maxLeftWidth, newLeftWidth));

        // Calculate percentage values
        const leftPercentage = (newLeftWidth / containerWidth * 100).toFixed(2) + '%';
        const rightPercentage = ((containerWidth - newLeftWidth) / containerWidth * 100).toFixed(2) + '%';

        // Apply new widths
        leftPanel.style.width = leftPercentage;
        rightPanel.style.width = rightPercentage;

        // Store the new widths
        this.defaultLeftPanelWidth = leftPercentage;
        this.defaultRightPanelWidth = rightPercentage;

        // Force change detection
        this.ngZone.run(() => {
          this.cdr.detectChanges();
        });

        animationFrameId = null;
      });
    };

    // Create mouseup handler
    const handleMouseUp = () => {
      this.logger.info('Ending resize operation');

      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
      }

      // Remove event listeners
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // CRITICAL FIX: Re-enable iframe pointer events after resize
      if (iframe) {
        iframe.style.pointerEvents = 'auto';
        this.logger.debug('Re-enabled iframe pointer events after resize');
      }

      // Remove resize overlay
      const overlay = document.querySelector('.test-resize-overlay');
      if (overlay) {
        overlay.remove();
        this.logger.debug('Removed resize overlay');
      }

      // Remove visual feedback
      leftPanel.classList.remove('dragging');
      rightPanel.classList.remove('dragging');
      resizer.classList.remove('active');
      document.body.classList.remove('user-select-none');

      // Reset state
      this.isResizing$.next(false);

      // Force change detection
      this.ngZone.run(() => {
        this.cdr.detectChanges();
      });
    };

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove, { passive: false });
    document.addEventListener('mouseup', handleMouseUp);
  }

  /**
   * Handle iframe load event
   */
  onIframeLoad(event: Event): void {
    const iframe = event.target as HTMLIFrameElement;
    if (iframe) {
      iframe.style.pointerEvents = 'auto';
      this.logger.info('Test iframe loaded successfully');

      try {
        const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
        if (iframeDoc) {
          // Add event listeners to prevent iframe interference during resize
          iframeDoc.addEventListener('mousedown', (e) => {
            if (this.isResizing$.value) {
              e.preventDefault();
              e.stopPropagation();
            }
          }, { passive: false });

          iframeDoc.addEventListener('mousemove', (e) => {
            if (this.isResizing$.value) {
              e.preventDefault();
              e.stopPropagation();
            }
          }, { passive: false });

          this.logger.debug('Added iframe event listeners for resizer compatibility');
        }
      } catch (error) {
        this.logger.debug('Cannot access iframe content (likely cross-origin):', error);
      }
    }
  }

  /**
   * Reset panel widths to default
   */
  resetPanels(): void {
    this.defaultLeftPanelWidth = '40%';
    this.defaultRightPanelWidth = '60%';

    const leftPanel = document.querySelector('.test-left-panel') as HTMLElement;
    const rightPanel = document.querySelector('.test-right-panel') as HTMLElement;

    if (leftPanel) leftPanel.style.width = this.defaultLeftPanelWidth;
    if (rightPanel) rightPanel.style.width = this.defaultRightPanelWidth;

    this.logger.info('Reset panels to default widths');
  }
}
