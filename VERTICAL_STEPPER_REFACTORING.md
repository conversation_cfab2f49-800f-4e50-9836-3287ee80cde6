# Vertical Stepper Component Refactoring

## Current Issues Analysis

The `VerticalStepperComponent` (1274 lines) has multiple responsibilities:
- State management (steps, current step, collapsed states)
- Timer management (elapsed time, completion time)
- Animation management (typewriter effects)
- API polling integration
- Error handling and retry logic
- UI rendering and event handling

## Refactoring Strategy: Separation of Concerns

### 1. Extract Timer Logic → StepperTimerService

```typescript
// stepper-timer.service.ts
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface TimerState {
  stepId: string;
  startTime: number;
  elapsedTime: number;
  isActive: boolean;
  completionTime?: number;
}

@Injectable({
  providedIn: 'root'
})
export class StepperTimerService {
  private timers = new Map<string, TimerState>();
  private timerInterval: any;
  private timersSubject = new BehaviorSubject<Map<string, TimerState>>(new Map());

  public timers$: Observable<Map<string, TimerState>> = this.timersSubject.asObservable();

  startTimer(stepId: string): void {
    const timer: TimerState = {
      stepId,
      startTime: Date.now(),
      elapsedTime: 0,
      isActive: true
    };

    this.timers.set(stepId, timer);
    this.emitTimersUpdate();
    this.ensureTimerRunning();
  }

  stopTimer(stepId: string): void {
    const timer = this.timers.get(stepId);
    if (timer && timer.isActive) {
      timer.isActive = false;
      timer.completionTime = Math.floor((Date.now() - timer.startTime) / 1000);
      timer.elapsedTime = timer.completionTime;
      this.emitTimersUpdate();
    }
  }

  getTimer(stepId: string): TimerState | undefined {
    return this.timers.get(stepId);
  }

  formatElapsedTime(elapsedTime: number): string {
    const minutes = Math.floor(elapsedTime / 60);
    const seconds = elapsedTime % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  private ensureTimerRunning(): void {
    if (!this.timerInterval) {
      this.timerInterval = setInterval(() => {
        this.updateActiveTimers();
      }, 1000);
    }
  }

  private updateActiveTimers(): void {
    const currentTime = Date.now();
    let hasActiveTimers = false;

    for (const timer of this.timers.values()) {
      if (timer.isActive) {
        timer.elapsedTime = Math.floor((currentTime - timer.startTime) / 1000);
        hasActiveTimers = true;
      }
    }

    if (!hasActiveTimers) {
      this.stopAllTimers();
    }

    this.emitTimersUpdate();
  }

  private stopAllTimers(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  private emitTimersUpdate(): void {
    this.timersSubject.next(new Map(this.timers));
  }

  destroy(): void {
    this.stopAllTimers();
    this.timers.clear();
  }
}
```

### 2. Extract State Management → StepperStateManager

```typescript
// stepper-state-manager.service.ts
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface StepperItem {
  id: string;
  title: string;
  description: string;
  visibleDescription: string;
  completed: boolean;
  active: boolean;
  collapsed?: boolean;
  isTyping?: boolean;
  retryCount?: number;
  isRetrying?: boolean;
}

export interface StepperState {
  steps: StepperItem[];
  currentStepIndex: number;
  currentStep: StepperItem | null;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  collapsedSteps: Set<number>;
}

@Injectable()
export class StepperStateManager {
  private initialState: StepperState = {
    steps: [],
    currentStepIndex: 0,
    currentStep: null,
    status: 'PENDING',
    collapsedSteps: new Set()
  };

  private stateSubject = new BehaviorSubject<StepperState>(this.initialState);
  public state$: Observable<StepperState> = this.stateSubject.asObservable();

  getCurrentState(): StepperState {
    return this.stateSubject.getValue();
  }

  addStep(step: Omit<StepperItem, 'id'>): string {
    const currentState = this.getCurrentState();
    const stepId = `step-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const newStep: StepperItem = {
      ...step,
      id: stepId,
      visibleDescription: '',
      isTyping: true
    };

    // Mark previous step as completed
    if (currentState.steps.length > 0) {
      const prevStepIndex = currentState.steps.length - 1;
      currentState.steps[prevStepIndex].completed = true;
      currentState.steps[prevStepIndex].active = false;
    }

    const newSteps = [...currentState.steps, newStep];
    const newCurrentStepIndex = newSteps.length - 1;

    this.updateState({
      steps: newSteps,
      currentStepIndex: newCurrentStepIndex,
      currentStep: newStep
    });

    return stepId;
  }

  updateStepDescription(stepIndex: number, description: string): void {
    const currentState = this.getCurrentState();
    if (stepIndex >= 0 && stepIndex < currentState.steps.length) {
      const updatedSteps = [...currentState.steps];
      updatedSteps[stepIndex] = {
        ...updatedSteps[stepIndex],
        description,
        visibleDescription: '',
        isTyping: true
      };

      this.updateState({ steps: updatedSteps });
    }
  }

  toggleStepCollapse(stepIndex: number): void {
    const currentState = this.getCurrentState();
    const newCollapsedSteps = new Set(currentState.collapsedSteps);
    
    if (newCollapsedSteps.has(stepIndex)) {
      // Expand this step, collapse others (except processing step)
      const processingStepIndex = currentState.status === 'IN_PROGRESS' ? currentState.currentStepIndex : -1;
      
      for (let i = 0; i < currentState.steps.length; i++) {
        if (i !== processingStepIndex) {
          newCollapsedSteps.add(i);
        }
      }
      newCollapsedSteps.delete(stepIndex);
    } else {
      // Collapse this step (if not processing)
      const processingStepIndex = currentState.status === 'IN_PROGRESS' ? currentState.currentStepIndex : -1;
      if (stepIndex !== processingStepIndex) {
        newCollapsedSteps.add(stepIndex);
      }
    }

    this.updateState({ collapsedSteps: newCollapsedSteps });
  }

  markStepAsCompleted(stepIndex: number): void {
    const currentState = this.getCurrentState();
    if (stepIndex >= 0 && stepIndex < currentState.steps.length) {
      const updatedSteps = [...currentState.steps];
      updatedSteps[stepIndex] = {
        ...updatedSteps[stepIndex],
        completed: true,
        active: false,
        isTyping: false
      };

      this.updateState({ steps: updatedSteps });
    }
  }

  setStatus(status: StepperState['status']): void {
    this.updateState({ status });
  }

  reset(): void {
    this.stateSubject.next(this.initialState);
  }

  private updateState(partialState: Partial<StepperState>): void {
    const currentState = this.getCurrentState();
    const newState = { ...currentState, ...partialState };
    this.stateSubject.next(newState);
  }
}
```

### 3. Refactored Main Component

```typescript
// vertical-stepper.component.ts (simplified)
import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { Observable } from 'rxjs';
import { StepperStateManager, StepperState } from './stepper-state-manager.service';
import { StepperTimerService } from './stepper-timer.service';
import { OptimizedTypewriterService } from '../services/optimized-typewriter.service';
import { UnifiedSubscriptionManager } from '../utils/unified-subscription-manager';

@Component({
  selector: 'app-vertical-stepper',
  templateUrl: './vertical-stepper.component.html',
  styleUrls: ['./vertical-stepper.component.scss'],
  providers: [StepperStateManager, UnifiedSubscriptionManager]
})
export class VerticalStepperComponent implements OnInit, OnDestroy {
  @Input() progress: string = '';
  @Input() progressDescription: string = '';
  @Input() status: string = 'PENDING';
  @Input() theme: 'light' | 'dark' = 'light';
  
  @Output() stepUpdated = new EventEmitter<number>();
  @Output() retryStep = new EventEmitter<number>();

  public state$: Observable<StepperState>;
  public timers$: Observable<Map<string, any>>;

  constructor(
    private stateManager: StepperStateManager,
    private timerService: StepperTimerService,
    private typewriterService: OptimizedTypewriterService,
    private subscriptionManager: UnifiedSubscriptionManager
  ) {
    this.state$ = this.stateManager.state$;
    this.timers$ = this.timerService.timers$;
  }

  ngOnInit(): void {
    this.subscriptionManager.subscribe(
      this.state$,
      (state) => this.handleStateChange(state)
    );
  }

  ngOnDestroy(): void {
    this.timerService.destroy();
    this.typewriterService.stopAllAnimations();
    this.subscriptionManager.destroy();
  }

  addStep(title: string, description: string): void {
    const stepId = this.stateManager.addStep({
      title,
      description,
      visibleDescription: '',
      completed: false,
      active: true
    });

    // Start timer for new step
    this.timerService.startTimer(stepId);
    
    // Start typewriter animation
    this.startTypewriterForStep(stepId, description);
  }

  toggleStepCollapse(stepIndex: number): void {
    this.stateManager.toggleStepCollapse(stepIndex);
  }

  onRetryClick(stepIndex: number): void {
    this.retryStep.emit(stepIndex);
  }

  private handleStateChange(state: StepperState): void {
    this.stepUpdated.emit(state.currentStepIndex);
  }

  private startTypewriterForStep(stepId: string, text: string): void {
    this.typewriterService.startAnimation(
      stepId,
      text,
      (visibleText) => {
        // Update visible description in state
        const currentState = this.stateManager.getCurrentState();
        const stepIndex = currentState.steps.findIndex(s => s.id === stepId);
        if (stepIndex >= 0) {
          // Update visible description logic here
        }
      },
      {
        speed: 50, // characters per second
        batchSize: 2,
        onComplete: () => {
          // Mark typing as complete
        }
      }
    );
  }
}
```

## Benefits of This Refactoring

### 1. Improved Testability
- Each service can be unit tested independently
- Mocking dependencies becomes straightforward
- Component logic is simplified and focused

### 2. Better Separation of Concerns
- Timer logic isolated and reusable
- State management centralized and predictable
- Animation logic optimized and consistent

### 3. Enhanced Maintainability
- Smaller, focused files easier to understand
- Clear interfaces between components
- Reduced coupling between concerns

### 4. Performance Improvements
- Optimized timer updates (only when needed)
- Efficient animation system
- Better memory management

### 5. Reusability
- Services can be used by other components
- Clear APIs for extending functionality
- Modular architecture supports future enhancements

## Migration Strategy

1. **Phase 1**: Create new services (1-2 days)
2. **Phase 2**: Refactor component to use services (2-3 days)
3. **Phase 3**: Update templates and styles (1 day)
4. **Phase 4**: Testing and optimization (1-2 days)

**Total Effort**: 5-8 days
**Risk Level**: Medium (requires careful testing)
**Impact**: High (significantly improved maintainability and performance)
