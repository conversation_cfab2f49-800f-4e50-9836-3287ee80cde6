<div class="test-split-container">
  <!-- Left Panel with Dummy Content -->
  <div class="test-left-panel" [style.width]="defaultLeftPanelWidth">
    <div class="test-panel-header">
      <h2>Split Screen Test</h2>
      <button class="reset-button" (click)="resetPanels()">Reset Panels</button>
    </div>
    
    <div class="test-panel-content">
      <div class="content-section" *ngFor="let section of dummyContent">
        <h3>{{ section.title }}</h3>
        <p [innerHTML]="section.content | nl2br"></p>
      </div>
      
      <div class="test-controls">
        <h3>Resizer Status</h3>
        <div class="status-indicator" [class.active]="isResizing$ | async">
          <span class="status-dot"></span>
          <span class="status-text">
            {{ (isResizing$ | async) ? 'Resizing Active' : 'Ready to Resize' }}
          </span>
        </div>
        
        <div class="panel-info">
          <p><strong>Left Panel:</strong> {{ defaultLeftPanelWidth }}</p>
          <p><strong>Right Panel:</strong> {{ defaultRightPanelWidth }}</p>
        </div>
      </div>
      
      <div class="test-instructions">
        <h3>How to Test</h3>
        <ol>
          <li>Hover over the resizer handle (vertical line between panels)</li>
          <li>Click and drag to resize panels</li>
          <li>Try dragging over the iframe area on the right</li>
          <li>Verify the resizer works smoothly in all areas</li>
          <li>Check that the iframe remains interactive after resizing</li>
        </ol>
      </div>
      
      <div class="feature-list">
        <h3>Features Being Tested</h3>
        <ul>
          <li>✅ Smooth resizing with requestAnimationFrame</li>
          <li>✅ Iframe pointer event management</li>
          <li>✅ Transparent overlay for event capture</li>
          <li>✅ Cross-origin iframe compatibility</li>
          <li>✅ Proper cleanup and memory management</li>
          <li>✅ Visual feedback during resize operations</li>
        </ul>
      </div>
    </div>
  </div>

  <!-- Resizer Handle -->
  <div 
    class="test-resizer" 
    (mousedown)="startResize($event)"
    [class.active]="isResizing$ | async"
    title="Drag to resize panels">
    <div class="resizer-handle"></div>
  </div>

  <!-- Right Panel with Iframe -->
  <div class="test-right-panel" [style.width]="defaultRightPanelWidth">
    <div class="test-panel-header">
      <h2>Iframe Preview</h2>
      <span class="iframe-url">{{ iframeUrl }}</span>
    </div>
    
    <div class="test-iframe-container">
      <iframe
        [src]="iframeUrl"
        class="test-iframe"
        frameborder="0"
        (load)="onIframeLoad($event)"
        sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-modals"
        referrerpolicy="no-referrer"
        allow="accelerometer; camera; encrypted-media; geolocation; gyroscope; microphone; midi">
      </iframe>
      
      <div class="iframe-overlay" *ngIf="isResizing$ | async">
        <div class="overlay-message">
          <i class="resize-icon">↔</i>
          <span>Resizing...</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Global Resize Indicator -->
<div class="global-resize-indicator" *ngIf="isResizing$ | async">
  <div class="indicator-content">
    <i class="resize-icon">↔</i>
    <span>Resizing Panels</span>
  </div>
</div>
