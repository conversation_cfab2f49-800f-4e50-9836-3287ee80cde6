# Test Split Screen Component - Usage Guide

## 🎯 Purpose

This component is specifically designed to test the resizer functionality with iframe content, particularly to verify the fixes implemented for the code-window component's resizer behavior when the preview tab contains an iframe.

## 🚀 How to Access

### Direct URL Access:
```
http://localhost:4200/test-resizer
```

### Navigation:
1. Start the Angular development server: `ng serve`
2. Navigate to `http://localhost:4200/test-resizer`
3. The test component will load with split screen layout

## 🧪 What to Test

### 1. **Basic Resizer Functionality**
- ✅ Hover over the vertical resizer handle between panels
- ✅ Click and drag to resize panels
- ✅ Verify smooth resizing with visual feedback
- ✅ Check that panels maintain minimum width constraints

### 2. **Iframe Interaction Testing**
- ✅ Try dragging the resizer over the iframe area on the right
- ✅ Verify that resizing works smoothly even when dragging over the iframe
- ✅ Check that the iframe remains interactive after resizing
- ✅ Ensure no mouse event conflicts between resizer and iframe

### 3. **Cross-Origin Iframe Testing**
- ✅ The component loads: `https://users-01df7a8f-8af7-477a-9e69-7d3a236fa774-96e39bd9-6-c70f3b.azurewebsites.net/`
- ✅ Verify resizer works with cross-origin iframe content
- ✅ Check that security restrictions don't break resizer functionality

### 4. **Visual Feedback Testing**
- ✅ Resizer handle should show hover effects
- ✅ Active resizing should show visual indicators
- ✅ Global resize indicator should appear during resize operations
- ✅ Iframe overlay should appear during resize to prevent interference

### 5. **Performance Testing**
- ✅ Resizing should be smooth with no lag or stuttering
- ✅ No memory leaks during extended resizing sessions
- ✅ Proper cleanup when component is destroyed

## 🔧 Features Being Tested

### **Implemented Fixes:**
1. **Iframe Pointer Event Management**
   - Disables iframe pointer events during resize
   - Re-enables them after resize completion

2. **Transparent Overlay System**
   - Creates overlay to capture mouse events over iframe
   - Removes overlay after resize completion

3. **Cross-Origin Compatibility**
   - Works with both same-origin and cross-origin iframes
   - Graceful fallback for security-restricted iframes

4. **Memory Management**
   - Proper cleanup of event listeners
   - Removal of dynamically created DOM elements
   - Prevention of memory leaks

5. **Performance Optimization**
   - RequestAnimationFrame for smooth animations
   - Throttled resize operations
   - Hardware acceleration support

## 📊 Expected Behavior

### **✅ PASS Criteria:**
- Resizer works consistently across all areas of the screen
- No interference between iframe and resizer functionality
- Smooth visual feedback during resize operations
- Iframe remains fully interactive after resizing
- No console errors or memory leaks
- Proper cursor states throughout the interaction

### **❌ FAIL Criteria:**
- Resizer stops working when dragging over iframe
- Iframe becomes unresponsive after resizing
- Console errors related to event handling
- Memory leaks or performance degradation
- Inconsistent behavior between different content types

## 🎨 Component Features

### **Left Panel:**
- **Test Instructions** - Step-by-step testing guide
- **Resizer Status** - Real-time status indicator
- **Panel Information** - Current panel width percentages
- **Feature List** - Overview of implemented features
- **Reset Button** - Reset panels to default widths

### **Right Panel:**
- **Iframe Preview** - Loads the specified URL
- **Iframe URL Display** - Shows the current iframe source
- **Resize Overlay** - Appears during resize operations

### **Resizer Handle:**
- **Visual Feedback** - Hover and active states
- **Smooth Dragging** - Hardware-accelerated resizing
- **Constraints** - Minimum and maximum width limits

## 🔍 Debugging Information

### **Console Logs:**
The component provides detailed logging for debugging:
- Resize operation start/end events
- Iframe pointer event state changes
- Overlay creation/removal events
- Error handling for cross-origin restrictions

### **Status Indicators:**
- **Green Dot** - Ready to resize
- **Yellow Pulsing Dot** - Actively resizing
- **Panel Width Display** - Real-time width percentages

## 🚨 Troubleshooting

### **If Resizer Doesn't Work:**
1. Check browser console for errors
2. Verify iframe URL is accessible
3. Try refreshing the page
4. Check if browser blocks cross-origin content

### **If Iframe Doesn't Load:**
1. Verify internet connection
2. Check if URL is accessible in new tab
3. Look for CORS or security restrictions
4. Try with a different test URL

### **Performance Issues:**
1. Check browser performance tab
2. Look for memory leaks in heap snapshots
3. Verify requestAnimationFrame is being used
4. Check for excessive DOM manipulations

## 🎯 Success Validation

The test is successful when:
1. ✅ Resizer works smoothly in all screen areas
2. ✅ Iframe remains interactive after resizing
3. ✅ No console errors or warnings
4. ✅ Visual feedback is consistent and responsive
5. ✅ Memory usage remains stable during extended testing

This test component validates that the resizer fixes implemented in the code-window component will work correctly with iframe content in the preview tab.
