import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetector<PERSON>ef, <PERSON><PERSON>one } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BehaviorSubject } from 'rxjs';
import { createLogger } from '../../utils';
import { Nl2brPipe } from '../../pipes/nl2br.pipe';

@Component({
  selector: 'app-test-split-screen',
  standalone: true,
  imports: [CommonModule, Nl2brPipe],
  templateUrl: './test-split-screen.component.html',
  styleUrls: ['./test-split-screen.component.scss']
})
export class TestSplitScreenComponent implements OnInit, OnDestroy {
  private readonly logger = createLogger('TestSplitScreenComponent');

  // Resizer state
  isResizing$ = new BehaviorSubject<boolean>(false);

  // Panel widths
  defaultLeftPanelWidth = '40%';
  defaultRightPanelWidth = '60%';

  // Iframe URL
  iframeUrl = 'https://users-01df7a8f-8af7-477a-9e69-7d3a236fa774-96e39bd9-6-c70f3b.azurewebsites.net/';

  // Dummy content for left panel
  dummyContent = [
    {
      title: 'Welcome to Split Screen Test',
      content: 'This is a test component to verify resizer functionality with iframe content.'
    },
    {
      title: 'Resizer Features',
      content: 'The resizer should work smoothly even when dragging over the iframe on the right side.'
    },
    {
      title: 'Test Instructions',
      content: '1. Try dragging the resizer handle\n2. Drag over the iframe area\n3. Verify smooth resizing behavior\n4. Check that iframe remains interactive after resize'
    },
    {
      title: 'Expected Behavior',
      content: 'The resizer should:\n• Work consistently across all areas\n• Not interfere with iframe interaction\n• Provide smooth visual feedback\n• Maintain proper cursor states'
    }
  ];

  constructor(
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone
  ) {}

  ngOnInit(): void {
    this.logger.info('TestSplitScreenComponent initialized');
  }

  ngOnDestroy(): void {
    // Clean up any active resize operation
    if (this.isResizing$.value) {
      const leftPanel = document.querySelector('.test-left-panel') as HTMLElement;
      const rightPanel = document.querySelector('.test-right-panel') as HTMLElement;
      const resizer = document.querySelector('.test-resizer') as HTMLElement;
      const iframe = document.querySelector('.test-iframe') as HTMLIFrameElement;
      const overlay = document.querySelector('.test-resize-overlay');

      if (leftPanel) leftPanel.classList.remove('dragging');
      if (rightPanel) rightPanel.classList.remove('dragging');
      if (resizer) resizer.classList.remove('active');

      // Re-enable iframe pointer events
      if (iframe) {
        iframe.style.pointerEvents = 'auto';
      }

      // Remove resize overlay
      if (overlay) {
        overlay.remove();
      }

      this.isResizing$.next(false);
    }

    this.logger.info('TestSplitScreenComponent destroyed');
  }

  /**
   * FORCE IMPLEMENTATION: Robust resizer that works in preview tab with iframe
   */
  startResize(event: MouseEvent): void {
    event.preventDefault();
    event.stopPropagation();

    this.logger.info('FORCE STARTING resize operation');
    this.isResizing$.next(true);

    // Get DOM elements with more robust selectors
    const leftPanel = document.querySelector('.test-left-panel') as HTMLElement;
    const rightPanel = document.querySelector('.test-right-panel') as HTMLElement;
    const resizer = document.querySelector('.test-resizer') as HTMLElement;
    const container = document.querySelector('.test-split-container') as HTMLElement;
    const iframe = document.querySelector('.test-iframe') as HTMLIFrameElement;

    if (!leftPanel || !rightPanel || !container || !resizer) {
      this.logger.error('Required DOM elements not found for resize operation');
      return;
    }

    // FORCE: Immediately disable ALL pointer events on iframe and its container
    const iframeContainer = document.querySelector('.test-iframe-container') as HTMLElement;
    if (iframe) {
      iframe.style.setProperty('pointer-events', 'none', 'important');
      iframe.style.setProperty('user-select', 'none', 'important');
      iframe.style.setProperty('-webkit-user-select', 'none', 'important');
      iframe.style.setProperty('-moz-user-select', 'none', 'important');
      iframe.style.setProperty('-ms-user-select', 'none', 'important');
      this.logger.debug('FORCE disabled iframe pointer events');
    }
    if (iframeContainer) {
      iframeContainer.style.setProperty('pointer-events', 'none', 'important');
      iframeContainer.style.setProperty('user-select', 'none', 'important');
    }

    // FORCE: Create multiple overlays for maximum event capture
    const overlays: HTMLElement[] = [];

    // Primary overlay over entire viewport
    const primaryOverlay = document.createElement('div');
    primaryOverlay.className = 'force-resize-overlay-primary';
    primaryOverlay.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      z-index: 999999 !important;
      cursor: col-resize !important;
      background: rgba(0, 123, 255, 0.05) !important;
      user-select: none !important;
      pointer-events: all !important;
      -webkit-user-select: none !important;
      -moz-user-select: none !important;
      -ms-user-select: none !important;
    `;
    document.body.appendChild(primaryOverlay);
    overlays.push(primaryOverlay);

    // Secondary overlay specifically over iframe area
    const iframeOverlay = document.createElement('div');
    iframeOverlay.className = 'force-resize-overlay-iframe';
    const iframeRect = iframe?.getBoundingClientRect();
    if (iframeRect) {
      iframeOverlay.style.cssText = `
        position: fixed !important;
        top: ${iframeRect.top}px !important;
        left: ${iframeRect.left}px !important;
        width: ${iframeRect.width}px !important;
        height: ${iframeRect.height}px !important;
        z-index: 1000000 !important;
        cursor: col-resize !important;
        background: rgba(255, 0, 0, 0.1) !important;
        user-select: none !important;
        pointer-events: all !important;
      `;
      document.body.appendChild(iframeOverlay);
      overlays.push(iframeOverlay);
    }

    // FORCE: Add global styles to prevent any interference
    const forceStyle = document.createElement('style');
    forceStyle.id = 'force-resize-styles';
    forceStyle.textContent = `
      * {
        user-select: none !important;
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
      }
      iframe, iframe * {
        pointer-events: none !important;
      }
      .force-resize-active {
        cursor: col-resize !important;
      }
    `;
    document.head.appendChild(forceStyle);

    // Add visual feedback
    resizer.classList.add('active');
    leftPanel.classList.add('dragging');
    rightPanel.classList.add('dragging');
    document.body.classList.add('user-select-none', 'force-resize-active');
    document.documentElement.classList.add('force-resize-active');

    // Store initial positions
    const initialX = event.clientX;
    const containerRect = container.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const initialLeftWidth = leftPanel.offsetWidth;
    const minWidth = 200;
    const maxLeftWidth = containerWidth - minWidth;
    const minLeftWidth = minWidth;

    this.logger.info(`Initial setup: containerWidth=${containerWidth}, initialLeftWidth=${initialLeftWidth}`);

    // Track animation frame for smoother performance
    let animationFrameId: number | null = null;
    let lastUpdateTime = 0;
    const throttleMs = 16; // ~60fps

    // FORCE: Enhanced mousemove handler with multiple event sources
    const handleMouseMove = (e: MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();

      const now = performance.now();
      if (now - lastUpdateTime < throttleMs && animationFrameId !== null) {
        return;
      }
      lastUpdateTime = now;

      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId);
      }

      animationFrameId = requestAnimationFrame(() => {
        const dx = e.clientX - initialX;
        let newLeftWidth = initialLeftWidth + dx;

        // Apply constraints
        newLeftWidth = Math.max(minLeftWidth, Math.min(maxLeftWidth, newLeftWidth));

        // Calculate percentage values
        const leftPercentage = (newLeftWidth / containerWidth * 100).toFixed(2) + '%';
        const rightPercentage = ((containerWidth - newLeftWidth) / containerWidth * 100).toFixed(2) + '%';

        // FORCE: Apply new widths with !important
        leftPanel.style.setProperty('width', leftPercentage, 'important');
        rightPanel.style.setProperty('width', rightPercentage, 'important');

        // Store the new widths
        this.defaultLeftPanelWidth = leftPercentage;
        this.defaultRightPanelWidth = rightPercentage;

        this.logger.debug(`Resizing: left=${leftPercentage}, right=${rightPercentage}, mouseX=${e.clientX}`);

        // Force change detection
        this.ngZone.run(() => {
          this.cdr.detectChanges();
        });

        animationFrameId = null;
      });
    };

    // FORCE: Enhanced mouseup handler
    const handleMouseUp = (e: MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      this.logger.info('FORCE ending resize operation');

      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
      }

      // Remove ALL event listeners from multiple sources
      document.removeEventListener('mousemove', handleMouseMove, true);
      document.removeEventListener('mouseup', handleMouseUp, true);
      document.removeEventListener('touchmove', handleTouchMove, true);
      document.removeEventListener('touchend', handleTouchEnd, true);

      window.removeEventListener('mousemove', handleMouseMove, true);
      window.removeEventListener('mouseup', handleMouseUp, true);
      window.removeEventListener('touchmove', handleTouchMove, true);
      window.removeEventListener('touchend', handleTouchEnd, true);

      // Remove from overlays
      overlays.forEach(overlay => {
        overlay.removeEventListener('mousemove', handleMouseMove, true);
        overlay.removeEventListener('mouseup', handleMouseUp, true);
        overlay.removeEventListener('touchmove', handleTouchMove, true);
        overlay.removeEventListener('touchend', handleTouchEnd, true);
      });

      // FORCE: Re-enable iframe pointer events
      if (iframe) {
        iframe.style.removeProperty('pointer-events');
        iframe.style.removeProperty('user-select');
        iframe.style.removeProperty('-webkit-user-select');
        iframe.style.removeProperty('-moz-user-select');
        iframe.style.removeProperty('-ms-user-select');
        this.logger.debug('FORCE re-enabled iframe pointer events');
      }
      if (iframeContainer) {
        iframeContainer.style.removeProperty('pointer-events');
        iframeContainer.style.removeProperty('user-select');
      }

      // Remove all overlays
      overlays.forEach(overlay => {
        if (overlay.parentNode) {
          overlay.parentNode.removeChild(overlay);
        }
      });

      // Remove force styles
      const forceStyleElement = document.getElementById('force-resize-styles');
      if (forceStyleElement) {
        forceStyleElement.remove();
      }

      // Remove visual feedback
      leftPanel.classList.remove('dragging');
      rightPanel.classList.remove('dragging');
      resizer.classList.remove('active');
      document.body.classList.remove('user-select-none', 'force-resize-active');
      document.documentElement.classList.remove('force-resize-active');

      // Reset state
      this.isResizing$.next(false);

      // Force change detection
      this.ngZone.run(() => {
        this.cdr.detectChanges();
      });

      this.logger.info('FORCE resize operation completed');
    };

    // FORCE: Touch event handlers for mobile support
    const handleTouchMove = (e: TouchEvent) => {
      e.preventDefault();
      e.stopPropagation();

      if (e.touches.length > 0) {
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent('mousemove', {
          clientX: touch.clientX,
          clientY: touch.clientY,
          bubbles: true,
          cancelable: true
        });
        handleMouseMove(mouseEvent);
      }
    };

    const handleTouchEnd = (e: TouchEvent) => {
      e.preventDefault();
      e.stopPropagation();

      const mouseEvent = new MouseEvent('mouseup', {
        bubbles: true,
        cancelable: true
      });
      handleMouseUp(mouseEvent);
    };

    // FORCE: Add event listeners to multiple sources for maximum capture
    document.addEventListener('mousemove', handleMouseMove, { passive: false, capture: true });
    document.addEventListener('mouseup', handleMouseUp, { passive: false, capture: true });
    document.addEventListener('touchmove', handleTouchMove, { passive: false, capture: true });
    document.addEventListener('touchend', handleTouchEnd, { passive: false, capture: true });

    window.addEventListener('mousemove', handleMouseMove, { passive: false, capture: true });
    window.addEventListener('mouseup', handleMouseUp, { passive: false, capture: true });
    window.addEventListener('touchmove', handleTouchMove, { passive: false, capture: true });
    window.addEventListener('touchend', handleTouchEnd, { passive: false, capture: true });

    // Add listeners to overlays as well
    overlays.forEach(overlay => {
      overlay.addEventListener('mousemove', handleMouseMove, { passive: false, capture: true });
      overlay.addEventListener('mouseup', handleMouseUp, { passive: false, capture: true });
      overlay.addEventListener('touchmove', handleTouchMove, { passive: false, capture: true });
      overlay.addEventListener('touchend', handleTouchEnd, { passive: false, capture: true });
    });

    this.logger.info('FORCE resize event listeners attached to multiple sources with touch support');
  }

  /**
   * Handle touch start for mobile devices
   */
  startTouchResize(event: TouchEvent): void {
    event.preventDefault();
    event.stopPropagation();

    if (event.touches.length > 0) {
      const touch = event.touches[0];
      const mouseEvent = new MouseEvent('mousedown', {
        clientX: touch.clientX,
        clientY: touch.clientY,
        bubbles: true,
        cancelable: true
      });
      this.startResize(mouseEvent);
    }
  }

  /**
   * Handle iframe load event
   */
  onIframeLoad(event: Event): void {
    const iframe = event.target as HTMLIFrameElement;
    if (iframe) {
      iframe.style.pointerEvents = 'auto';
      this.logger.info('Test iframe loaded successfully');

      try {
        const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
        if (iframeDoc) {
          // Add event listeners to prevent iframe interference during resize
          iframeDoc.addEventListener('mousedown', (e) => {
            if (this.isResizing$.value) {
              e.preventDefault();
              e.stopPropagation();
            }
          }, { passive: false });

          iframeDoc.addEventListener('mousemove', (e) => {
            if (this.isResizing$.value) {
              e.preventDefault();
              e.stopPropagation();
            }
          }, { passive: false });

          this.logger.debug('Added iframe event listeners for resizer compatibility');
        }
      } catch (error) {
        this.logger.debug('Cannot access iframe content (likely cross-origin):', error);
      }
    }
  }

  /**
   * Reset panel widths to default
   */
  resetPanels(): void {
    this.defaultLeftPanelWidth = '40%';
    this.defaultRightPanelWidth = '60%';

    const leftPanel = document.querySelector('.test-left-panel') as HTMLElement;
    const rightPanel = document.querySelector('.test-right-panel') as HTMLElement;

    if (leftPanel) leftPanel.style.width = this.defaultLeftPanelWidth;
    if (rightPanel) rightPanel.style.width = this.defaultRightPanelWidth;

    this.logger.info('Reset panels to default widths');
  }
}
