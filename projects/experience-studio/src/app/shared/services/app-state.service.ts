import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { createLogger } from '../utils';


// Define interfaces for the application state
export interface ProjectState {
  projectId: string | null;
  jobId: string | null;
  userId: string | null;
  prompt: string | null;
  application: string | null;
  technology: string | null;
  designLibrary: string | null;
  imageUrl: string | null;
  imageDataUri: string | null;
  type: string | null;
  codeGenerated: boolean;
  generatedCode: any;
}

export interface UIState {
  hasSelectedCard: boolean;
  hasSubmittedPrompt: boolean;
  currentView: 'preview' | 'code' | 'logs' | 'artifacts';
  isLoading: boolean;
  stepperShouldReset: boolean;
  currentProjectId: string;
  deployedUrl: string | null;
  showHistory: boolean;
  isLeftPanelCollapsed: boolean;
  isRightPanelCollapsed: boolean;
}

export interface AppState {
  project: ProjectState;
  ui: UIState;
}

// Initial state values
const initialProjectState: ProjectState = {
  projectId: null,
  jobId: null,
  userId: null,
  prompt: null,
  application: null,
  technology: null,
  designLibrary: null,
  imageUrl: null,
  imageDataUri: null,
  type: null,
  codeGenerated: false,
  generatedCode: null
};

const initialUIState: UIState = {
  hasSelectedCard: false,
  hasSubmittedPrompt: false,
  currentView: 'preview',
  isLoading: false,
  stepperShouldReset: false,
  currentProjectId: '',
  deployedUrl: null,
  showHistory: false,
  isLeftPanelCollapsed: false,
  isRightPanelCollapsed: false
};

const initialAppState: AppState = {
  project: initialProjectState,
  ui: initialUIState
};

@Injectable({
  providedIn: 'root'
})
export class AppStateService {
  // Private BehaviorSubject to hold the current state
  private state = new BehaviorSubject<AppState>(this.loadStateFromStorage() || initialAppState);

  // Public Observable for components to subscribe to
  public state$ = this.state.asObservable();

  private logger = createLogger('AppStateService');

  // Project state as an Observable
  public project$ = this.state$.pipe(
    map(state => state.project)
  );

  // UI state as an Observable
  public ui$ = this.state$.pipe(
    map(state => state.ui)
  );

  // Specific UI state observables for better performance
  public hasSelectedCard$ = this.ui$.pipe(map(ui => ui.hasSelectedCard));
  public hasSubmittedPrompt$ = this.ui$.pipe(map(ui => ui.hasSubmittedPrompt));
  public currentView$ = this.ui$.pipe(map(ui => ui.currentView));
  public isLoading$ = this.ui$.pipe(map(ui => ui.isLoading));
  public deployedUrl$ = this.ui$.pipe(map(ui => ui.deployedUrl));
  public showHistory$ = this.ui$.pipe(map(ui => ui.showHistory));

  constructor() {
    // Subscribe to state changes to persist to sessionStorage
    this.state$.subscribe(state => {
      this.saveStateToStorage(state);
    });
  }

  /**
   * Get the current state value
   */
  public getState(): AppState {
    return this.state.getValue();
  }

  /**
   * Get the current project state
   */
  public getProjectState(): ProjectState {
    return this.getState().project;
  }

  /**
   * Get the current UI state
   */
  public getUIState(): UIState {
    return this.getState().ui;
  }

  /**
   * Update the project state
   * @param projectState Partial project state to update
   */
  public updateProjectState(projectState: Partial<ProjectState>): void {
    const currentState = this.getState();
    this.state.next({
      ...currentState,
      project: {
        ...currentState.project,
        ...projectState
      }
    });
  }

  /**
   * Set project ID and job ID
   * @param projectId Project ID
   * @param jobId Job ID
   */
  public setProjectAndJobId(projectId: string, jobId: string): void {
    this.updateProjectState({ projectId, jobId });
  }

  /**
   * Set user ID
   * @param userId User ID
   */
  public setUserId(userId: string): void {
    this.updateProjectState({ userId });
  }

  /**
   * Set generated code
   * @param code Generated code
   */
  public setGeneratedCode(code: any): void {
    this.updateProjectState({
      generatedCode: code,
      codeGenerated: true
    });
  }

  /**
   * Set complete project selections
   * @param selections Complete project selections
   */
  public setCompleteSelections(selections: any): void {
    // Get the current state to preserve the user ID if it's not provided in selections
    const currentState = this.getProjectState();

    this.updateProjectState({
      projectId: selections.projectId || null,
      jobId: selections.jobId || null,
      userId: selections.userId || currentState.userId, // Preserve user ID if not provided
      prompt: selections.prompt || null,
      application: selections.application || null,
      technology: selections.technology || null,
      designLibrary: selections.designLibrary || null,
      imageUrl: selections.imageUrl || null,
      imageDataUri: selections.imageDataUri || null,
      type: selections.type || null,
      codeGenerated: selections.codeGenerated || false,
      generatedCode: selections.generatedCode || null
    });
  }

  /**
   * Reset the project state to initial values
   */
  public resetProjectState(): void {
    this.updateProjectState(initialProjectState);
  }

  /**
   * Update the UI state
   * @param uiState Partial UI state to update
   */
  public updateUIState(uiState: Partial<UIState>): void {
    const currentState = this.getState();
    this.state.next({
      ...currentState,
      ui: {
        ...currentState.ui,
        ...uiState
      }
    });
  }

  /**
   * Set card selection status (consolidates CardSelectionService)
   * @param selected Whether a card is selected
   */
  public setCardSelected(selected: boolean = true): void {
    this.updateUIState({ hasSelectedCard: selected });
  }

  /**
   * Set prompt submission status (consolidates PromptSubmissionService)
   * @param submitted Whether a prompt has been submitted
   */
  public setPromptSubmitted(submitted: boolean = true): void {
    this.updateUIState({ hasSubmittedPrompt: submitted });
  }

  /**
   * Set the current view
   * @param view The current view to display
   */
  public setCurrentView(view: UIState['currentView']): void {
    this.updateUIState({ currentView: view });
  }

  /**
   * Set loading state
   * @param isLoading Whether the application is loading
   */
  public setLoading(isLoading: boolean): void {
    this.updateUIState({ isLoading });
  }

  /**
   * Set deployed URL (consolidates CodeSharingService functionality)
   * @param url The deployed application URL
   */
  public setDeployedUrl(url: string | null): void {
    this.updateUIState({ deployedUrl: url });
  }

  /**
   * Toggle history panel visibility
   * @param show Whether to show the history panel
   */
  public setShowHistory(show: boolean): void {
    this.updateUIState({ showHistory: show });
  }

  /**
   * Trigger stepper reset (consolidates StepperStateService)
   */
  public triggerStepperReset(): void {
    this.updateUIState({ stepperShouldReset: true });
    // Reset flag after a short delay to allow components to react
    setTimeout(() => {
      this.updateUIState({ stepperShouldReset: false });
    }, 100);
  }

  /**
   * Set panel collapse states
   * @param leftCollapsed Whether the left panel is collapsed
   * @param rightCollapsed Whether the right panel is collapsed
   */
  public setPanelStates(leftCollapsed?: boolean, rightCollapsed?: boolean): void {
    const updates: Partial<UIState> = {};
    if (leftCollapsed !== undefined) updates.isLeftPanelCollapsed = leftCollapsed;
    if (rightCollapsed !== undefined) updates.isRightPanelCollapsed = rightCollapsed;
    this.updateUIState(updates);
  }

  /**
   * Reset the UI state to initial values
   */
  public resetUIState(): void {
    this.updateUIState(initialUIState);
  }

  /**
   * Save state to sessionStorage
   * @param state Current application state
   */
  private saveStateToStorage(state: AppState): void {
    try {
      // Store project state in sessionStorage for backward compatibility
      sessionStorage.setItem('completeSelections', JSON.stringify(state.project));

      // Also store the full state for future use
      sessionStorage.setItem('appState', JSON.stringify(state));
    } catch (e) {
      this.logger.error('Error saving state to sessionStorage:', e);
    }
  }

  /**
   * Load state from sessionStorage
   * @returns Loaded application state or null if not found
   */
  private loadStateFromStorage(): AppState | null {
    try {
      // Try to load the full state first
      const storedState = sessionStorage.getItem('appState');
      if (storedState) {
        return JSON.parse(storedState);
      }

      // Fall back to loading just the project state for backward compatibility
      const storedSelections = sessionStorage.getItem('completeSelections');
      if (storedSelections) {
        const projectState = JSON.parse(storedSelections);
        return {
          project: {
            ...initialProjectState,
            ...projectState
          },
          ui: initialUIState
        };
      }
    } catch (e) {
      this.logger.error('Error loading state from sessionStorage:', e);
    }

    return null;
  }
}
