import { Injectable } from '@angular/core';
import { createLogger } from '../utils/logger';

/**
 * Configuration interface for typewriter animations
 */
export interface TypewriterConfig {
  /** Characters per second */
  speed: number;
  /** Characters to type per animation frame */
  batchSize: number;
  /** Callback for progress updates (0-1) */
  onProgress?: (progress: number) => void;
  /** Callback when animation completes */
  onComplete?: () => void;
  /** Callback for each character typed */
  onCharacter?: (char: string, index: number) => void;
}

/**
 * Default typewriter configuration
 */
const DEFAULT_CONFIG: TypewriterConfig = {
  speed: 50, // 50 characters per second
  batchSize: 2, // Type 2 characters per frame for smooth animation
};

/**
 * Internal state for tracking animation progress
 */
interface AnimationState {
  id: string;
  text: string;
  currentIndex: number;
  callback: (visibleText: string) => void;
  config: TypewriterConfig;
  lastFrameTime: number;
  charAccumulator: number;
  isPaused: boolean;
}

/**
 * Optimized typewriter service using requestAnimationFrame for smooth animations
 * Replaces setTimeout-based implementation for better performance
 * 
 * Features:
 * - RequestAnimationFrame-based animation loop
 * - Batched character updates for performance
 * - Multiple concurrent animations
 * - Pause/resume functionality
 * - Progress tracking
 * - Memory leak prevention
 */
@Injectable({
  providedIn: 'root'
})
export class OptimizedTypewriterService {
  private readonly logger = createLogger('OptimizedTypewriterService');
  
  private readonly activeAnimations = new Map<string, AnimationState>();
  private animationFrame: number | null = null;
  private isDestroyed = false;

  /**
   * Start a typewriter animation
   * @param id Unique identifier for the animation
   * @param text Text to animate
   * @param callback Function called with visible text on each update
   * @param config Animation configuration
   */
  startAnimation(
    id: string,
    text: string,
    callback: (visibleText: string) => void,
    config: Partial<TypewriterConfig> = {}
  ): void {
    if (this.isDestroyed) {
      this.logger.warn('Attempting to start animation after service destroyed');
      return;
    }

    // Stop existing animation with same ID
    this.stopAnimation(id);

    // Merge with default config
    const finalConfig: TypewriterConfig = { ...DEFAULT_CONFIG, ...config };

    // Validate configuration
    if (finalConfig.speed <= 0) {
      this.logger.error('Invalid speed configuration:', finalConfig.speed);
      return;
    }

    if (finalConfig.batchSize <= 0) {
      this.logger.error('Invalid batch size configuration:', finalConfig.batchSize);
      return;
    }

    // Create animation state
    const state: AnimationState = {
      id,
      text,
      currentIndex: 0,
      callback,
      config: finalConfig,
      lastFrameTime: performance.now(),
      charAccumulator: 0,
      isPaused: false
    };

    this.activeAnimations.set(id, state);
    
    this.logger.debug(`Starting animation "${id}" with ${text.length} characters`);
    
    // Start animation loop if not running
    if (!this.animationFrame) {
      this.startAnimationLoop();
    }

    // Call callback immediately with empty string
    callback('');
  }

  /**
   * Stop a specific animation
   * @param id Animation ID to stop
   */
  stopAnimation(id: string): void {
    const state = this.activeAnimations.get(id);
    if (state) {
      this.logger.debug(`Stopping animation "${id}"`);
      this.activeAnimations.delete(id);
      
      // Call completion callback if configured
      if (state.config.onComplete) {
        state.config.onComplete();
      }
    }
  }

  /**
   * Pause a specific animation
   * @param id Animation ID to pause
   */
  pauseAnimation(id: string): void {
    const state = this.activeAnimations.get(id);
    if (state) {
      state.isPaused = true;
      this.logger.debug(`Paused animation "${id}"`);
    }
  }

  /**
   * Resume a paused animation
   * @param id Animation ID to resume
   */
  resumeAnimation(id: string): void {
    const state = this.activeAnimations.get(id);
    if (state && state.isPaused) {
      state.isPaused = false;
      state.lastFrameTime = performance.now(); // Reset timing
      this.logger.debug(`Resumed animation "${id}"`);
      
      // Restart animation loop if needed
      if (!this.animationFrame) {
        this.startAnimationLoop();
      }
    }
  }

  /**
   * Stop all active animations
   */
  stopAllAnimations(): void {
    this.logger.debug(`Stopping ${this.activeAnimations.size} active animations`);
    
    // Call completion callbacks
    this.activeAnimations.forEach(state => {
      if (state.config.onComplete) {
        state.config.onComplete();
      }
    });
    
    this.activeAnimations.clear();
    
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }
  }

  /**
   * Get the current progress of an animation
   * @param id Animation ID
   * @returns Progress value between 0 and 1, or null if animation not found
   */
  getProgress(id: string): number | null {
    const state = this.activeAnimations.get(id);
    if (!state) return null;
    
    return state.text.length > 0 ? state.currentIndex / state.text.length : 1;
  }

  /**
   * Check if an animation is currently running
   * @param id Animation ID
   * @returns True if animation is active and not paused
   */
  isAnimationActive(id: string): boolean {
    const state = this.activeAnimations.get(id);
    return state ? !state.isPaused && state.currentIndex < state.text.length : false;
  }

  /**
   * Get statistics about active animations
   */
  getStats(): {
    activeAnimations: number;
    pausedAnimations: number;
    totalCharacters: number;
  } {
    let pausedCount = 0;
    let totalChars = 0;
    
    this.activeAnimations.forEach(state => {
      if (state.isPaused) pausedCount++;
      totalChars += state.text.length;
    });
    
    return {
      activeAnimations: this.activeAnimations.size,
      pausedAnimations: pausedCount,
      totalCharacters: totalChars
    };
  }

  /**
   * Start the main animation loop
   */
  private startAnimationLoop(): void {
    const animate = (currentTime: number) => {
      if (this.isDestroyed) {
        return;
      }

      let hasActiveAnimations = false;

      for (const [id, state] of this.activeAnimations) {
        if (this.updateAnimation(state, currentTime)) {
          hasActiveAnimations = true;
        } else {
          // Animation completed
          this.activeAnimations.delete(id);
          if (state.config.onComplete) {
            state.config.onComplete();
          }
        }
      }

      if (hasActiveAnimations) {
        this.animationFrame = requestAnimationFrame(animate);
      } else {
        this.animationFrame = null;
      }
    };

    this.animationFrame = requestAnimationFrame(animate);
  }

  /**
   * Update a single animation state
   * @param state Animation state to update
   * @param currentTime Current timestamp
   * @returns True if animation should continue, false if completed
   */
  private updateAnimation(state: AnimationState, currentTime: number): boolean {
    // Skip paused animations
    if (state.isPaused) {
      state.lastFrameTime = currentTime;
      return true;
    }

    // Check if animation is complete
    if (state.currentIndex >= state.text.length) {
      return false;
    }

    const deltaTime = currentTime - state.lastFrameTime;
    const charsPerMs = state.config.speed / 1000;
    
    // Accumulate characters to type based on elapsed time
    state.charAccumulator += deltaTime * charsPerMs;
    
    // Type characters in batches for performance
    const charsToType = Math.min(
      Math.floor(state.charAccumulator),
      state.config.batchSize,
      state.text.length - state.currentIndex
    );

    if (charsToType > 0) {
      const oldIndex = state.currentIndex;
      state.currentIndex += charsToType;
      state.charAccumulator -= charsToType;
      
      // Get visible text
      const visibleText = state.text.substring(0, state.currentIndex);
      
      // Call main callback
      state.callback(visibleText);
      
      // Call character callbacks if configured
      if (state.config.onCharacter) {
        for (let i = oldIndex; i < state.currentIndex; i++) {
          state.config.onCharacter(state.text[i], i);
        }
      }
      
      // Call progress callback if configured
      if (state.config.onProgress) {
        const progress = state.currentIndex / state.text.length;
        state.config.onProgress(progress);
      }
    }

    state.lastFrameTime = currentTime;
    return state.currentIndex < state.text.length;
  }

  /**
   * Cleanup method to prevent memory leaks
   */
  destroy(): void {
    this.logger.info('Destroying OptimizedTypewriterService');
    this.isDestroyed = true;
    this.stopAllAnimations();
  }
}
