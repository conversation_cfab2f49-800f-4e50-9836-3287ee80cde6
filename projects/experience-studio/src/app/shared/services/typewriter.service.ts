import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface TypewriterConfig {
  typingSpeed?: number;
  pauseBetweenItems?: number;
  onCharacterTyped?: (char: string, fullText: string, currentIndex: number) => void;
  onItemComplete?: (item: any, index: number) => void;
  onAllComplete?: () => void;
}

export interface TypewriterItem {
  id: string;
  content: string;
  type?: 'text' | 'code' | 'markdown';
  visibleContent?: string;
  isTyping?: boolean;
  isComplete?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class TypewriterService {
  // Placeholder functionality (existing)
  private placeholderSubject = new BehaviorSubject<string>('');
  public placeholder$: Observable<string> = this.placeholderSubject.asObservable();

  private texts: string[] = [];
  private staticText: string = '';
  private currentTextIndex = 0;
  private currentCharIndex = 0;
  private isTyping = true;
  private animationTimeout: any;
  private pauseTimeout: any;

  // New unified typewriter functionality
  private activeAnimations = new Map<string, any>();
  private animationCallbacks = new Map<string, TypewriterConfig>();

  constructor() {}

  /** Start typewriter animation */
  startTypewriter(
    texts: string[],
    staticText: string = '',
    typingSpeed: number = 40,
    erasingSpeed: number = 30,
    pauseBeforeErasing: number = 400,
    pauseBeforeTyping: number = 200
  ): void {
    if (!texts || texts.length === 0) {
      return;
    }

    this.texts = this.shuffleArray([...texts]);
    this.staticText = staticText;
    this.currentTextIndex = 0;
    this.currentCharIndex = 0;
    this.isTyping = true;
    this.stopTypewriter();
    this.typeEffect(typingSpeed, erasingSpeed, pauseBeforeErasing, pauseBeforeTyping);
  }

  /** Fisher-Yates shuffle */
  private shuffleArray<T>(array: T[]): T[] {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
  }

  /** Stop animation */
  stopTypewriter(): void {
    if (this.animationTimeout) {
      clearTimeout(this.animationTimeout);
      this.animationTimeout = null;
    }

    if (this.pauseTimeout) {
      clearTimeout(this.pauseTimeout);
      this.pauseTimeout = null;
    }
  }

  /**
   * Start unified typewriter animation for multiple items
   * @param animationId Unique identifier for this animation
   * @param items Array of items to animate
   * @param config Configuration options
   */
  startUnifiedTypewriter(
    animationId: string,
    items: TypewriterItem[],
    config: TypewriterConfig = {}
  ): void {
    // Stop any existing animation with this ID
    this.stopUnifiedTypewriter(animationId);

    // Set default configuration
    const finalConfig: TypewriterConfig = {
      typingSpeed: 20, // Same as vertical stepper default
      pauseBetweenItems: 200,
      ...config
    };

    // Store the configuration
    this.animationCallbacks.set(animationId, finalConfig);

    // Initialize all items
    items.forEach(item => {
      item.visibleContent = '';
      item.isTyping = true;
      item.isComplete = false;
    });

    // Start the animation
    this.animateItems(animationId, items, 0, 0, finalConfig);
  }

  /**
   * Stop unified typewriter animation
   * @param animationId Unique identifier for the animation to stop
   */
  stopUnifiedTypewriter(animationId: string): void {
    const animation = this.activeAnimations.get(animationId);
    if (animation) {
      clearTimeout(animation);
      this.activeAnimations.delete(animationId);
    }
    this.animationCallbacks.delete(animationId);
  }

  /**
   * Stop all active animations
   */
  stopAllAnimations(): void {
    this.activeAnimations.forEach((animation) => {
      clearTimeout(animation);
    });
    this.activeAnimations.clear();
    this.animationCallbacks.clear();
  }

  /**
   * Core animation method for typing items character by character
   * @param animationId Unique identifier for this animation
   * @param items Array of items to animate
   * @param currentItemIndex Index of the current item being typed
   * @param currentCharIndex Index of the current character being typed
   * @param config Configuration options
   */
  private animateItems(
    animationId: string,
    items: TypewriterItem[],
    currentItemIndex: number,
    currentCharIndex: number,
    config: TypewriterConfig
  ): void {
    // Check if animation was stopped
    if (!this.animationCallbacks.has(animationId)) {
      return;
    }

    // Check if we've finished all items
    if (currentItemIndex >= items.length) {
      // Mark all items as complete
      items.forEach(item => {
        item.isTyping = false;
        item.isComplete = true;
      });

      // Call completion callback
      if (config.onAllComplete) {
        config.onAllComplete();
      }

      // Clean up
      this.stopUnifiedTypewriter(animationId);
      return;
    }

    const currentItem = items[currentItemIndex];
    const content = currentItem.content || '';

    // Check if we've finished typing this item
    if (currentCharIndex >= content.length) {
      // Mark current item as complete
      currentItem.isTyping = false;
      currentItem.isComplete = true;
      currentItem.visibleContent = content;

      // Call item completion callback
      if (config.onItemComplete) {
        config.onItemComplete(currentItem, currentItemIndex);
      }

      // Move to next item after pause
      const timeout = setTimeout(() => {
        this.animateItems(animationId, items, currentItemIndex + 1, 0, config);
      }, config.pauseBetweenItems || 200);

      this.activeAnimations.set(animationId, timeout);
      return;
    }

    // Type the next character
    const nextChar = content[currentCharIndex];
    currentItem.visibleContent = content.substring(0, currentCharIndex + 1);

    // Call character typed callback
    if (config.onCharacterTyped) {
      config.onCharacterTyped(nextChar, currentItem.visibleContent, currentCharIndex);
    }

    // Determine typing speed based on content type and character
    let typingSpeed = config.typingSpeed || 20;

    if (currentItem.type === 'code') {
      // Adjust speed for code content
      if (nextChar === ' ' || nextChar === '\n' || nextChar === '\t') {
        typingSpeed = typingSpeed * 0.5; // Faster for whitespace
      } else if (/[{}\[\]()<>]/.test(nextChar)) {
        typingSpeed = typingSpeed * 1.5; // Slower for brackets
      }
    }

    // Schedule next character
    const timeout = setTimeout(() => {
      this.animateItems(animationId, items, currentItemIndex, currentCharIndex + 1, config);
    }, typingSpeed);

    this.activeAnimations.set(animationId, timeout);
  }

  /** Core animation logic */
  private typeEffect(
    typingSpeed: number,
    erasingSpeed: number,
    pauseBeforeErasing: number,
    pauseBeforeTyping: number
  ): void {
    const currentText = this.texts[this.currentTextIndex];

    if (this.isTyping) {
      this.currentCharIndex++;
      const animatedPart = currentText.slice(0, this.currentCharIndex);
      this.placeholderSubject.next(this.staticText ? `${this.staticText} ${animatedPart}` : animatedPart);

      if (this.currentCharIndex >= currentText.length) {
        this.isTyping = false;
        this.pauseTimeout = setTimeout(() => {
          this.typeEffect(typingSpeed, erasingSpeed, pauseBeforeErasing, pauseBeforeTyping);
        }, pauseBeforeErasing);
        return;
      }
    } else {
      this.currentCharIndex--;
      const animatedPart = currentText.slice(0, this.currentCharIndex);
      this.placeholderSubject.next(this.staticText ? `${this.staticText} ${animatedPart}` : animatedPart);

      if (this.currentCharIndex <= 0) {
        this.isTyping = true;
        this.currentTextIndex = (this.currentTextIndex + 1) % this.texts.length;
        this.pauseTimeout = setTimeout(() => {
          this.typeEffect(typingSpeed, erasingSpeed, pauseBeforeErasing, pauseBeforeTyping);
        }, pauseBeforeTyping);
        return;
      }
    }

    this.animationTimeout = setTimeout(
      () => this.typeEffect(typingSpeed, erasingSpeed, pauseBeforeErasing, pauseBeforeTyping),
      this.isTyping ? typingSpeed : erasingSpeed
    );
  }
}
