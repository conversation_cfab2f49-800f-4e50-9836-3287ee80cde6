.test-split-container {
  display: flex;
  height: 100vh;
  width: 100%;
  background: #f5f5f5;
  position: relative;
  overflow: hidden;

  // Prevent text selection during resize
  &.resizing * {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    pointer-events: none !important;
  }
}

.test-left-panel {
  background: #ffffff;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  transition: width 0.1s ease;
  min-width: 200px;
  max-width: calc(100% - 200px);

  &.dragging {
    transition: none;
    user-select: none;
  }
}

.test-right-panel {
  background: #fafafa;
  display: flex;
  flex-direction: column;
  transition: width 0.1s ease;
  min-width: 200px;
  max-width: calc(100% - 200px);

  &.dragging {
    transition: none;
    user-select: none;
  }
}

.test-panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 60px;

  h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .reset-button {
    padding: 8px 16px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;

    &:hover {
      background: #0056b3;
    }

    &:active {
      transform: translateY(1px);
    }
  }

  .iframe-url {
    font-size: 12px;
    color: #666;
    font-family: monospace;
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 3px;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.test-panel-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;

  .content-section {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #007bff;

    h3 {
      margin: 0 0 12px 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    p {
      margin: 0;
      line-height: 1.6;
      color: #555;
      white-space: pre-line;
    }
  }

  .test-controls {
    background: #e8f4fd;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 24px;

    h3 {
      margin: 0 0 12px 0;
      font-size: 16px;
      color: #0056b3;
    }

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;

      .status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #28a745;
        transition: background-color 0.2s ease;
      }

      &.active .status-dot {
        background: #ffc107;
        animation: pulse 1s infinite;
      }

      .status-text {
        font-weight: 500;
        color: #333;
      }
    }

    .panel-info {
      font-family: monospace;
      font-size: 14px;
      color: #666;

      p {
        margin: 4px 0;
      }
    }
  }

  .test-instructions {
    background: #fff3cd;
    padding: 16px;
    border-radius: 6px;
    border-left: 4px solid #ffc107;
    margin-bottom: 24px;

    h3 {
      margin: 0 0 12px 0;
      color: #856404;
    }

    ol {
      margin: 0;
      padding-left: 20px;
      color: #856404;

      li {
        margin-bottom: 8px;
        line-height: 1.5;
      }
    }
  }

  .feature-list {
    background: #d4edda;
    padding: 16px;
    border-radius: 6px;
    border-left: 4px solid #28a745;

    h3 {
      margin: 0 0 12px 0;
      color: #155724;
    }

    ul {
      margin: 0;
      padding-left: 20px;
      color: #155724;

      li {
        margin-bottom: 6px;
        line-height: 1.5;
      }
    }
  }
}

.test-resizer {
  width: 6px;
  background: #dee2e6;
  cursor: col-resize;
  position: relative;
  transition: background-color 0.2s ease;
  flex-shrink: 0;

  &:hover {
    background: #007bff;
  }

  &.active {
    background: #0056b3;
    box-shadow: 0 0 0 1px #0056b3;
  }

  .resizer-handle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 40px;
    background: #6c757d;
    border-radius: 10px;
    opacity: 0;
    transition: opacity 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
      content: '⋮⋮';
      color: white;
      font-size: 12px;
      letter-spacing: -2px;
    }
  }

  &:hover .resizer-handle,
  &.active .resizer-handle {
    opacity: 1;
  }
}

.test-iframe-container {
  flex: 1;
  position: relative;
  overflow: hidden;

  .test-iframe {
    width: 100%;
    height: 100%;
    border: none;
    display: block;
    transition: pointer-events 0.1s ease;

    &[style*="pointer-events: none"] {
      pointer-events: none !important;
    }
  }

  .iframe-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 123, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;

    .overlay-message {
      background: rgba(0, 123, 255, 0.9);
      color: white;
      padding: 12px 20px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      .resize-icon {
        font-size: 18px;
        animation: resize-pulse 1s infinite;
      }
    }
  }
}

.global-resize-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  background: rgba(0, 123, 255, 0.95);
  color: white;
  padding: 12px 20px;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);

  .indicator-content {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;

    .resize-icon {
      font-size: 16px;
      animation: resize-pulse 1s infinite;
    }
  }
}

// FORCE: Enhanced resize overlay styles (created dynamically)
:global(.test-resize-overlay),
:global(.force-resize-overlay-primary),
:global(.force-resize-overlay-iframe) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 999999 !important;
  cursor: col-resize !important;
  background: transparent !important;
  user-select: none !important;
  pointer-events: all !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

// FORCE: Specific styling for iframe overlay
:global(.force-resize-overlay-iframe) {
  background: rgba(255, 0, 0, 0.05) !important;
  border: 2px dashed rgba(255, 0, 0, 0.3) !important;
  z-index: 1000000 !important;
}

// FORCE: Primary overlay styling
:global(.force-resize-overlay-primary) {
  background: rgba(0, 123, 255, 0.02) !important;
  z-index: 999999 !important;
}

// FORCE: Global styles for resize prevention and iframe control
:global(body.user-select-none),
:global(body.force-resize-active),
:global(html.force-resize-active) {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  cursor: col-resize !important;
}

// FORCE: Iframe control during resize
:global(body.force-resize-active iframe),
:global(body.force-resize-active iframe *) {
  pointer-events: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

// FORCE: Enhanced resizer visibility during active state
:global(.force-resize-active) .test-resizer {
  background: #007bff !important;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.3) !important;
  width: 8px !important;
}

// FORCE: Ensure panels maintain their sizing during force resize
.test-left-panel[style*="width"],
.test-right-panel[style*="width"] {
  transition: none !important;

  &.dragging {
    transition: none !important;
    user-select: none !important;
    pointer-events: none !important;

    * {
      user-select: none !important;
      pointer-events: none !important;
    }
  }
}

// Animations
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes resize-pulse {
  0%, 100% { transform: scaleX(1); }
  50% { transform: scaleX(1.2); }
}

// Responsive design
@media (max-width: 768px) {
  .test-split-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  .test-left-panel,
  .test-right-panel {
    width: 100% !important;
    min-width: auto;
    max-width: none;
  }

  .test-resizer {
    display: none;
  }

  .test-panel-header {
    .iframe-url {
      display: none;
    }
  }
}
